/* SMART Canvas Main Container */
.smart-canvas-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  background: var(--canvas-bg, #f8f9fa);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  color: var(--text-primary, #2d3748);
  overflow: hidden;
}

.smart-canvas-container:focus {
  outline: 2px solid var(--focus-color, #4299e1);
  outline-offset: -2px;
}

/* Canvas Main Area */
.canvas-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  min-width: 0;
}

/* Context Bar Placeholder */
.context-bar-placeholder {
  height: 60px;
  background: var(--canvas-bg, #f8f9fa);
  border-bottom: 1px solid var(--border-color, #e2e8f0);
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.canvas-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: var(--text-secondary, #718096);
}

.canvas-info span {
  font-weight: 500;
}

/* Connections Placeholder */
.connections-placeholder {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 5;
}

.connection-info {
  background: var(--primary-color, #4299e1);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.canvas-workspace {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: var(--canvas-workspace-bg, #ffffff);
  border: 1px solid var(--border-color, #e2e8f0);
  cursor: grab;
}

.canvas-workspace:active {
  cursor: grabbing;
}

/* Canvas Grid */
.canvas-grid {
  width: 100%;
  height: 100%;
  position: relative;
  background-image: 
    radial-gradient(circle, var(--grid-dot-color, #cbd5e0) 1px, transparent 1px);
  background-size: 20px 20px;
  transform-origin: 0 0;
  transition: transform 0.1s ease-out;
}

.canvas-grid.drop-target {
  background-color: var(--drop-target-bg, rgba(66, 153, 225, 0.1));
}

.grid-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.canvas-elements {
  position: relative;
  z-index: 1;
}

/* Canvas Block Base Styles */
.canvas-block,
.canvas-element {
  position: absolute;
  background: var(--block-bg, #ffffff);
  border: 2px solid var(--block-border, #e2e8f0);
  border-radius: 12px;
  box-shadow: var(--block-shadow, 0 4px 6px -1px rgba(0, 0, 0, 0.1));
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 280px;
  max-width: 400px;
  user-select: none;
}

/* Element specific styles */
.element-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--border-color, #e2e8f0);
  background: var(--block-header-bg, #f7fafc);
  border-radius: 10px 10px 0 0;
}

.element-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
}

.element-type-badge {
  background: var(--primary-color, #4299e1);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.element-content {
  padding: 16px;
}

.element-field {
  margin-bottom: 12px;
}

.element-field:last-child {
  margin-bottom: 0;
}

.field-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary, #718096);
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.field-value {
  font-size: 14px;
  color: var(--text-primary, #2d3748);
}

/* Status indicators */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-not-started .status-dot {
  background: var(--gray-400, #a0aec0);
}

.status-in-progress .status-dot {
  background: var(--warning-color, #ed8936);
}

.status-completed .status-dot {
  background: var(--success-color, #48bb78);
}

/* Priority indicators */
.priority-indicator {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 3px;
}

.priority-low {
  background: var(--success-light, #c6f6d5);
  color: var(--success-dark, #22543d);
}

.priority-medium {
  background: var(--warning-light, #faf089);
  color: var(--warning-dark, #744210);
}

.priority-high {
  background: var(--error-light, #fed7d7);
  color: var(--error-dark, #742a2a);
}

/* Progress bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--gray-200, #edf2f7);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--primary-color, #4299e1);
  transition: width 0.3s ease;
}

.canvas-block:hover,
.canvas-element:hover {
  border-color: var(--block-border-hover, #cbd5e0);
  box-shadow: var(--block-shadow-hover, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
  transform: translateY(-2px);
}

.canvas-block.selected,
.canvas-element.selected {
  border-color: var(--primary-color, #4299e1);
  box-shadow: var(--block-shadow-selected, 0 0 0 3px rgba(66, 153, 225, 0.1));
}

.canvas-block.dragging,
.canvas-element.dragging {
  opacity: 0.8;
  transform: rotate(5deg);
  z-index: 1000;
}

/* Block Header */
.block-header {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color, #e2e8f0);
  background: var(--block-header-bg, #f7fafc);
  border-radius: 10px 10px 0 0;
}

.block-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  color: var(--primary-color, #4299e1);
}

.block-title {
  flex: 1;
  min-width: 0;
}

.block-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.title-input {
  width: 100%;
  border: none;
  background: transparent;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #2d3748);
  outline: none;
  border-bottom: 2px solid var(--primary-color, #4299e1);
}

.block-actions {
  display: flex;
  gap: 8px;
}

.block-actions button {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: var(--button-bg, #edf2f7);
  color: var(--text-secondary, #4a5568);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.block-actions button:hover {
  background: var(--button-bg-hover, #e2e8f0);
  color: var(--text-primary, #2d3748);
}

.save-btn {
  background: var(--success-color, #48bb78) !important;
  color: white !important;
}

.cancel-btn {
  background: var(--error-color, #f56565) !important;
  color: white !important;
}

/* Block Content */
.block-content {
  padding: 16px;
}

/* Block Footer */
.block-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid var(--border-color, #e2e8f0);
  background: var(--block-footer-bg, #f7fafc);
  border-radius: 0 0 10px 10px;
  font-size: 12px;
  color: var(--text-secondary, #718096);
}

.paim-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
}

.paim-hint {
  font-style: italic;
  color: var(--primary-color, #4299e1);
}

/* Connection Points */
.connection-point {
  position: absolute;
  width: 12px;
  height: 12px;
  background: var(--primary-color, #4299e1);
  border: 2px solid var(--canvas-workspace-bg, #ffffff);
  border-radius: 50%;
  cursor: crosshair;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10;
}

.canvas-block:hover .connection-point,
.canvas-block.selected .connection-point {
  opacity: 1;
}

.connection-point.top {
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.connection-point.right {
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.connection-point.bottom {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
}

.connection-point.left {
  left: -6px;
  top: 50%;
  transform: translateY(-50%);
}

.connection-point:hover {
  background: var(--success-color, #48bb78);
  transform: scale(1.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .smart-canvas-container {
    flex-direction: column;
  }
  
  .canvas-block {
    min-width: 240px;
    max-width: 320px;
  }
  
  .block-header {
    padding: 12px;
  }
  
  .block-content {
    padding: 12px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .smart-canvas-container {
    --canvas-bg: #1a202c;
    --canvas-workspace-bg: #2d3748;
    --text-primary: #f7fafc;
    --text-secondary: #a0aec0;
    --border-color: #4a5568;
    --block-bg: #2d3748;
    --block-border: #4a5568;
    --block-header-bg: #1a202c;
    --block-footer-bg: #1a202c;
    --grid-dot-color: #4a5568;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .canvas-block {
    border-width: 3px;
  }
  
  .canvas-block.selected {
    border-width: 4px;
  }
  
  .connection-point {
    border-width: 3px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .canvas-block,
  .connection-point,
  .canvas-grid {
    transition: none;
  }
}