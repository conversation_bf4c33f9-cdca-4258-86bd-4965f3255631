import React, { useRef, useEffect } from 'react';
import { useDrop } from 'react-dnd';
import { SmartGoalBlock } from './blocks/SmartGoalBlockSimple';

export const CanvasGrid = ({
  elements,
  selectedElements,
  onElementSelect,
  onElementMove,
  onElementDrop,
  viewport,
  onViewportChange
}) => {
  const canvasRef = useRef(null);

  const [{ isOver }, drop] = useDrop({
    accept: ['smart-goal', 'task', 'kpi', 'brand-asset'],
    drop: (item, monitor) => {
      const offset = monitor.getClientOffset();
      const canvasRect = canvasRef.current.getBoundingClientRect();

      const position = {
        x: (offset.x - canvasRect.left - viewport.x) / viewport.zoom,
        y: (offset.y - canvasRect.top - viewport.y) / viewport.zoom
      };

      // Call the parent's drop handler
      if (onElementDrop) {
        onElementDrop(item.type, position);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver()
    })
  });

  const renderElement = (element) => {
    const isSelected = selectedElements.includes(element.id);
    
    const elementStyle = {
      position: 'absolute',
      left: element.position.x + viewport.x,
      top: element.position.y + viewport.y,
      transform: `scale(${viewport.zoom})`,
      transformOrigin: 'top left',
      zIndex: isSelected ? 100 : 1
    };

    const handleClick = (e) => {
      e.stopPropagation();
      onElementSelect(element.id, e.ctrlKey || e.metaKey);
    };

    const handleMouseDown = (e) => {
      if (e.button !== 0) return; // Only left click
      
      const startX = e.clientX;
      const startY = e.clientY;
      const startElementX = element.position.x;
      const startElementY = element.position.y;

      const handleMouseMove = (e) => {
        const deltaX = e.clientX - startX;
        const deltaY = e.clientY - startY;
        
        const newPosition = {
          x: startElementX + deltaX / viewport.zoom,
          y: startElementY + deltaY / viewport.zoom
        };
        
        onElementMove(element.id, newPosition);
      };

      const handleMouseUp = () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    };

    switch (element.type) {
      case 'smart-goal':
        return (
          <div
            key={element.id}
            style={elementStyle}
            onClick={handleClick}
            onMouseDown={handleMouseDown}
          >
            <SmartGoalBlock 
              data={element.data} 
              isSelected={isSelected}
            />
          </div>
        );
      
      case 'task':
        return (
          <div
            key={element.id}
            style={elementStyle}
            onClick={handleClick}
            onMouseDown={handleMouseDown}
            className={`canvas-element element-task ${isSelected ? 'selected' : ''}`}
          >
            <div className="element-header">
              <h3 className="element-title">{element.data.title}</h3>
              <span className="element-type-badge">Task</span>
            </div>
            <div className="element-content">
              <div className="element-field">
                <div className="field-label">Description</div>
                <div className="field-value">{element.data.description || 'No description'}</div>
              </div>
              <div className="element-field">
                <div className="field-label">Status</div>
                <div className={`status-indicator status-${element.data.status}`}>
                  <div className="status-dot"></div>
                  {element.data.status?.replace('-', ' ') || 'Not started'}
                </div>
              </div>
              <div className="element-field">
                <div className="field-label">Priority</div>
                <div className={`priority-indicator priority-${element.data.priority}`}>
                  {element.data.priority || 'Medium'}
                </div>
              </div>
            </div>
          </div>
        );
      
      case 'kpi':
        return (
          <div
            key={element.id}
            style={elementStyle}
            onClick={handleClick}
            onMouseDown={handleMouseDown}
            className={`canvas-element element-kpi ${isSelected ? 'selected' : ''}`}
          >
            <div className="element-header">
              <h3 className="element-title">{element.data.title}</h3>
              <span className="element-type-badge">KPI</span>
            </div>
            <div className="element-content">
              <div className="element-field">
                <div className="field-label">Current Value</div>
                <div className="field-value">
                  {element.data.currentValue || 0} {element.data.unit || ''}
                </div>
              </div>
              <div className="element-field">
                <div className="field-label">Target Value</div>
                <div className="field-value">
                  {element.data.targetValue || 100} {element.data.unit || ''}
                </div>
              </div>
              <div className="element-field">
                <div className="field-label">Progress</div>
                <div className="progress-bar">
                  <div 
                    className="progress-fill" 
                    style={{ 
                      width: `${Math.min(100, (element.data.currentValue / element.data.targetValue) * 100)}%` 
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        );
      
      case 'brand-asset':
        return (
          <div
            key={element.id}
            style={elementStyle}
            onClick={handleClick}
            onMouseDown={handleMouseDown}
            className={`canvas-element element-brand-asset ${isSelected ? 'selected' : ''}`}
          >
            <div className="element-header">
              <h3 className="element-title">{element.data.title}</h3>
              <span className="element-type-badge">Brand Asset</span>
            </div>
            <div className="element-content">
              <div className="element-field">
                <div className="field-label">Asset Type</div>
                <div className="field-value">{element.data.assetType || 'Unknown'}</div>
              </div>
              <div className="element-field">
                <div className="field-label">Tags</div>
                <div className="field-value">
                  {element.data.tags?.join(', ') || 'No tags'}
                </div>
              </div>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div 
      ref={(node) => {
        canvasRef.current = node;
        drop(node);
      }}
      className={`canvas-grid ${isOver ? 'drop-target' : ''}`}
      style={{
        position: 'relative',
        width: '100%',
        height: '100%',
        overflow: 'hidden'
      }}
    >
      {elements.map(renderElement)}
      
      <style jsx>{`
        .canvas-grid {
          cursor: grab;
        }
        
        .canvas-grid:active {
          cursor: grabbing;
        }
        
        .canvas-grid.drop-target {
          background-color: rgba(0, 123, 255, 0.05);
        }
        
        .canvas-element {
          width: 250px;
          min-height: 180px;
          cursor: move;
          user-select: none;
        }
        
        .canvas-element:hover {
          z-index: 50 !important;
        }
      `}</style>
    </div>
  );
};