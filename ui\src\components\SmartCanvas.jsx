import React, { useState, useCallback, useRef } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useSmartCanvasStore } from '../store/smartCanvasStore';
import { CanvasGrid } from './CanvasGrid';
import { ComponentSidebar } from './ComponentSidebar';
// import { ContextBar } from './ContextBar'; // TODO: Implement
import { PAIMAssistant } from './PAIMAssistant';
// import { ConnectionRenderer } from './ConnectionRenderer'; // TODO: Implement
import './styles/SmartCanvas.css';

const SmartCanvas = () => {
  const canvasRef = useRef(null);
  const [selectedElements, setSelectedElements] = useState([]);
  const [isMultiSelect, setIsMultiSelect] = useState(false);
  
  const {
    elements,
    connections,
    viewport,
    addElement,
    updateElement,
    removeElement,
    updateViewport,
    connectElements,
    disconnectElements
  } = useSmartCanvasStore();

  const handleElementDrop = useCallback((elementType, position) => {
    const newElement = {
      id: `${elementType}-${Date.now()}`,
      type: elementType,
      position,
      data: getDefaultDataForType(elementType),
      createdAt: new Date().toISOString()
    };
    addElement(newElement);
  }, [addElement]);

  const handleElementSelect = useCallback((elementId, multiSelect = false) => {
    if (multiSelect) {
      setSelectedElements(prev => 
        prev.includes(elementId) 
          ? prev.filter(id => id !== elementId)
          : [...prev, elementId]
      );
    } else {
      setSelectedElements([elementId]);
    }
  }, []);

  const handleElementMove = useCallback((elementId, newPosition) => {
    updateElement(elementId, { position: newPosition });
  }, [updateElement]);

  const handleCanvasClick = useCallback((event) => {
    if (event.target === canvasRef.current) {
      setSelectedElements([]);
    }
  }, []);

  const handleKeyDown = useCallback((event) => {
    // Keyboard shortcuts
    if (event.key === 'Delete' && selectedElements.length > 0) {
      selectedElements.forEach(elementId => removeElement(elementId));
      setSelectedElements([]);
    }
    
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'a':
          event.preventDefault();
          setSelectedElements(elements.map(el => el.id));
          break;
        case 'z':
          event.preventDefault();
          // TODO: Implement undo
          break;
        case 'y':
          event.preventDefault();
          // TODO: Implement redo
          break;
      }
    }
  }, [selectedElements, elements, removeElement]);

  return (
    <DndProvider backend={HTML5Backend}>
      <div 
        className="smart-canvas-container"
        onKeyDown={handleKeyDown}
        tabIndex={0}
        role="application"
        aria-label="SMART Canvas Workspace"
      >
        <ComponentSidebar onElementDrop={handleElementDrop} />
        
        <div className="canvas-main">
          {/* TODO: Implement ContextBar */}
          <div className="context-bar-placeholder">
            <div className="canvas-info">
              <span>Elements: {elements.length}</span>
              <span>Selected: {selectedElements.length}</span>
              <span>Zoom: {Math.round(viewport.zoom * 100)}%</span>
            </div>
          </div>

          <div
            ref={canvasRef}
            className="canvas-workspace"
            onClick={handleCanvasClick}
            role="main"
            aria-label="Canvas workspace for SMART goals and tasks"
          >
            <CanvasGrid
              elements={elements}
              selectedElements={selectedElements}
              onElementSelect={handleElementSelect}
              onElementMove={handleElementMove}
              onElementDrop={handleElementDrop}
              viewport={viewport}
              onViewportChange={updateViewport}
            />

            {/* TODO: Implement ConnectionRenderer */}
            <div className="connections-placeholder">
              {connections.length > 0 && (
                <div className="connection-info">
                  {connections.length} connection(s)
                </div>
              )}
            </div>
          </div>
        </div>
        
        <PAIMAssistant 
          selectedElements={selectedElements}
          canvasState={{ elements, connections }}
        />
      </div>
    </DndProvider>
  );
};

function getDefaultDataForType(elementType) {
  switch (elementType) {
    case 'smart-goal':
      return {
        title: 'New SMART Goal',
        specific: '',
        measurable: '',
        achievable: '',
        relevant: '',
        timeBound: { startDate: null, endDate: null },
        progress: 0
      };
    case 'task':
      return {
        title: 'New Task',
        description: '',
        dueDate: null,
        priority: 'medium',
        status: 'not-started',
        assignee: '',
        isRecurring: false
      };
    case 'kpi':
      return {
        title: 'New KPI',
        metricType: 'percentage',
        currentValue: 0,
        targetValue: 100,
        unit: '%',
        visualization: 'progress-bar'
      };
    case 'brand-asset':
      return {
        title: 'New Brand Asset',
        assetType: 'logo',
        fileUrl: null,
        tags: []
      };
    default:
      return {};
  }
}

export default SmartCanvas;