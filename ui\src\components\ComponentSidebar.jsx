import React from 'react';
import { useDrag } from 'react-dnd';

const DraggableComponent = ({ type, name, description, icon, onElementDrop }) => {
  const [{ isDragging }, drag] = useDrag({
    type: type,
    item: { type },
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult();
      if (item && dropResult && onElementDrop) {
        // This will be handled by the drop target
      }
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  });

  return (
    <div
      ref={drag}
      className={`component-item ${isDragging ? 'dragging' : ''}`}
      style={{ opacity: isDragging ? 0.5 : 1 }}
    >
      <div className="component-icon">
        {icon}
      </div>
      <div className="component-info">
        <div className="component-name">{name}</div>
        <div className="component-description">{description}</div>
      </div>
    </div>
  );
};

export const ComponentSidebar = ({ onElementDrop }) => {
  const components = [
    {
      category: 'Planning',
      items: [
        {
          type: 'smart-goal',
          name: 'SMART Goal',
          description: 'Specific, Measurable, Achievable, Relevant, Time-bound goal',
          icon: (
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"/>
            </svg>
          )
        },
        {
          type: 'task',
          name: 'Task',
          description: 'Individual action item or deliverable',
          icon: (
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z"/>
            </svg>
          )
        }
      ]
    },
    {
      category: 'Metrics',
      items: [
        {
          type: 'kpi',
          name: 'KPI',
          description: 'Key Performance Indicator with targets',
          icon: (
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M16,6L18.29,8.29L13.41,13.17L9.41,9.17L2,16.59L3.41,18L9.41,12L13.41,16L19.71,9.71L22,12V6H16Z"/>
            </svg>
          )
        }
      ]
    },
    {
      category: 'Assets',
      items: [
        {
          type: 'brand-asset',
          name: 'Brand Asset',
          description: 'Brand materials and resources',
          icon: (
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
            </svg>
          )
        }
      ]
    }
  ];

  return (
    <div className="component-sidebar">
      <div className="sidebar-header">
        <h2>Components</h2>
        <p>Drag components to canvas</p>
      </div>
      
      <div className="sidebar-content">
        {components.map((category) => (
          <div key={category.category} className="component-category">
            <h3 className="category-title">{category.category}</h3>
            <div className="component-list">
              {category.items.map((component) => (
                <DraggableComponent
                  key={component.type}
                  type={component.type}
                  name={component.name}
                  description={component.description}
                  icon={component.icon}
                  onElementDrop={onElementDrop}
                />
              ))}
            </div>
          </div>
        ))}
      </div>
      
      <div className="sidebar-footer">
        <div className="help-text">
          <p><strong>Tip:</strong> Drag any component to the canvas to create it</p>
          <p>Hold <kbd>Ctrl</kbd> to select multiple elements</p>
          <p>Press <kbd>Delete</kbd> to remove selected elements</p>
        </div>
      </div>
      
      <style jsx>{`
        .component-sidebar {
          width: 280px;
          background: white;
          border-right: 1px solid #e1e5e9;
          display: flex;
          flex-direction: column;
          box-shadow: 2px 0 4px rgba(0, 0, 0, 0.05);
          z-index: 10;
        }
        
        .sidebar-header {
          padding: 20px;
          border-bottom: 1px solid #e1e5e9;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        }
        
        .sidebar-header h2 {
          margin: 0 0 4px 0;
          font-size: 18px;
          font-weight: 600;
        }
        
        .sidebar-header p {
          margin: 0;
          font-size: 14px;
          opacity: 0.9;
        }
        
        .sidebar-content {
          flex: 1;
          padding: 20px;
          overflow-y: auto;
        }
        
        .component-category {
          margin-bottom: 24px;
        }
        
        .category-title {
          font-size: 14px;
          font-weight: 600;
          color: #495057;
          margin-bottom: 12px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
        
        .component-list {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }
        
        .component-item {
          padding: 12px 16px;
          border: 1px solid #e1e5e9;
          border-radius: 6px;
          background: #f8f9fa;
          cursor: grab;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          gap: 12px;
        }
        
        .component-item:hover {
          background: #e9ecef;
          border-color: #adb5bd;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .component-item:active,
        .component-item.dragging {
          cursor: grabbing;
          transform: translateY(0);
        }
        
        .component-icon {
          width: 20px;
          height: 20px;
          color: #6c757d;
          flex-shrink: 0;
        }
        
        .component-info {
          flex: 1;
          min-width: 0;
        }
        
        .component-name {
          font-size: 14px;
          font-weight: 500;
          color: #495057;
          margin-bottom: 2px;
        }
        
        .component-description {
          font-size: 12px;
          color: #6c757d;
          line-height: 1.3;
        }
        
        .sidebar-footer {
          padding: 16px 20px;
          border-top: 1px solid #e1e5e9;
          background: #f8f9fa;
        }
        
        .help-text {
          font-size: 12px;
          color: #6c757d;
          line-height: 1.4;
        }
        
        .help-text p {
          margin: 0 0 6px 0;
        }
        
        .help-text p:last-child {
          margin-bottom: 0;
        }
        
        .help-text kbd {
          background: #e9ecef;
          border: 1px solid #adb5bd;
          border-radius: 3px;
          padding: 1px 4px;
          font-size: 11px;
          font-family: monospace;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
          .component-sidebar {
            width: 100%;
            height: 200px;
            border-right: none;
            border-bottom: 1px solid #e1e5e9;
          }
          
          .sidebar-content {
            padding: 12px;
          }
          
          .component-list {
            flex-direction: row;
            overflow-x: auto;
            gap: 12px;
          }
          
          .component-item {
            min-width: 150px;
            flex-shrink: 0;
          }
          
          .sidebar-footer {
            display: none;
          }
        }
      `}</style>
    </div>
  );
};